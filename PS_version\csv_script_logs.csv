"Timestamp","Event","Status","Details"
"12/08/2025 19:33:27","Credentials","Info","Loading API credentials"
"12/08/2025 19:33:27","Credentials","Info","API credentials loaded"
"12/08/2025 19:33:27","Token Request","Info","Requesting Battle.net access token"
"12/08/2025 19:33:28","Token Request","Info","Battle.net access token received"
"12/08/2025 19:33:28","Raiders","Info","Loading raiders"
"12/08/2025 19:33:28","Raiders","Info","Raiders loaded"
"12/08/2025 19:33:28","Data","Info","Getting character data"
"12/08/2025 19:34:00","Data","Info","Character data loaded"
"12/08/2025 19:34:00","Export","Info","Exporting full data"
"12/08/2025 19:34:00","Export","Info","Full data exported"
"12/08/2025 19:34:00","Import","Info","Importing data"
"12/08/2025 19:34:00","Import","Info","Data imported"
"12/08/2025 19:34:00","Overrides","Info","Loading role overrides"
"12/08/2025 19:34:00","Overrides","Info","Role overrides loaded: 7 entries"
"12/08/2025 19:34:00","Table","Info","Creating table"
"12/08/2025 19:34:01","Override","Info","Applied override for Drexyl: Ranged DPS/Demonology -> Ranged DPS/Potato"
"12/08/2025 19:34:01","Override","Info","Applied override for Shalicia: Tank/Vengeance -> Melee DPS/Havoc"
"12/08/2025 19:34:01","Override","Info","Applied override for Somith: Ranged DPS/Augmentation -> Ranged DPS/Augmentation"
"12/08/2025 19:34:01","Override","Info","Applied override for Xervaz: Tank/Blood -> Melee DPS/Frost"
"12/08/2025 19:34:01","Override","Info","Applied override for Macdrako: Healer/Preservation -> Ranged DPS/Devastation"
"12/08/2025 19:34:01","Override","Info","Applied override for Pallav: Tank/Protection -> Melee DPS/Retribution"
"12/08/2025 19:34:01","Table","Info","Table created"
"12/08/2025 19:34:01","Export","Info","Exporting character table"
"12/08/2025 19:34:01","Export","Info","Character table exported"
"12/08/2025 19:34:01","Table","Info","Creating tier table"
"12/08/2025 19:34:01","Table","Info","Tier table created"
"12/08/2025 19:34:01","Export","Info","Exporting tier table"
"12/08/2025 19:34:01","Export","Info","Tier table exported"
"12/08/2025 19:34:01","Table","Info","Creating enchant table"
"12/08/2025 19:34:01","Table","Info","Enchant table created"
"12/08/2025 19:34:01","Export","Info","Exporting enchant table"
"12/08/2025 19:34:01","Export","Info","Enchant table exported"
"12/08/2025 19:34:01","Logs","Info","Fetching data from WarcraftLogs API..."
"12/08/2025 19:34:04","Logs","Info","Data fetched from WarcraftLogs API"
