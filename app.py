from flask import Flask, render_template, send_from_directory, request, jsonify, redirect, url_for, session
import os
import subprocess
from datetime import datetime, timedelta
from functools import wraps
import pandas as pd
import requests
import json

app = Flask(__name__)

# Configuration management
def load_config():
    """Load configuration from config.json file"""
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        # Return default config if file doesn't exist
        return {
            "discord": {
                "raider_role_id": "1173303263832064050",
                "guild_id": "451810734129676298",
                "support_role_id": "1060628081359982714",
                "ticket_category_id": "451810734129676300",
                "target_server_id": "451810734129676298"
            },
            "warcraft_logs": {
                "client_id": "9dc8aaec-23bc-443e-8eef-8054a905cb82",
                "client_secret": "TIu343dsvPAGWa4O86S9MuLHsnASI1iRIFjU4Ki5",
                "guild_id": 689141
            },
            "game_settings": {
                "tier_ilvl": 684,
                "ticket_cooldown": 300,
                "max_tickets_per_user": 1,
                "weathered_crest_id": 41786,
                "carved_crest_id": 41789,
                "runed_crest_id": 41790,
                "gilded_crest_id": 41791
            },
            "app_settings": {
                "password": "uproar321",
                "sudo_password": "",
                "host": "*************",
                "port": 5555,
                "debug": True
            }
        }

def save_config(config):
    """Save configuration to config.json file"""
    with open('config.json', 'w') as f:
        json.dump(config, f, indent=2)

# Load initial configuration
config = load_config()
app.secret_key = config['app_settings']['password']

DISCORD_BOT_TOKEN = os.getenv('DISCORD_BOT_TOKEN')
DISCORD_GUILD_ID = os.getenv('DISCORD_GUILD_ID') or config['discord']['guild_id']
DISCORD_RAIDER_ROLE_ID = config['discord']['raider_role_id']
SUDO_PASSWORD = os.getenv('SUDO_PASSWORD')

def assign_discord_role(user_id: str) -> bool:
    config = load_config()  # Reload config to get latest values
    guild_id = DISCORD_GUILD_ID or config['discord']['guild_id']
    raider_role_id = config['discord']['raider_role_id']

    if not DISCORD_BOT_TOKEN or not guild_id:
        return False

    url = f"https://discord.com/api/v10/guilds/{guild_id}/members/{user_id}/roles/{raider_role_id}"
    headers = {
        "Authorization": f"Bot {DISCORD_BOT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        bot_info_url = "https://discord.com/api/v10/users/@me"
        bot_response = requests.get(bot_info_url, headers=headers)
        bot_response.raise_for_status()
        
        member_url = f"https://discord.com/api/v10/guilds/{guild_id}/members/{user_id}"
        member_response = requests.get(member_url, headers=headers)
        member_response.raise_for_status()

        roles_url = f"https://discord.com/api/v10/guilds/{guild_id}/roles"
        roles_response = requests.get(roles_url, headers=headers)
        roles_response.raise_for_status()

        bot_roles = member_response.json().get('roles', [])
        bot_role_positions = [role['position'] for role in roles_response.json() if role['id'] in bot_roles]
        target_role = next((role for role in roles_response.json() if role['id'] == raider_role_id), None)
        
        if not target_role or not bot_role_positions or max(bot_role_positions) <= target_role['position']:
            return False
        
        response = requests.put(url, headers=headers)
        response.raise_for_status()
        return True
        
    except requests.exceptions.RequestException:
        return False

def remove_discord_role(user_id: str) -> bool:
    if not DISCORD_BOT_TOKEN or not DISCORD_GUILD_ID:
        return False
        
    url = f"https://discord.com/api/v10/guilds/{DISCORD_GUILD_ID}/members/{user_id}/roles/{DISCORD_RAIDER_ROLE_ID}"
    headers = {
        "Authorization": f"Bot {DISCORD_BOT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.delete(url, headers=headers)
        response.raise_for_status()
        return True
    except requests.exceptions.RequestException:
        return False

def requires_password(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('authenticated'):
            # For API endpoints (JSON requests), return JSON error
            if request.is_json or request.path.startswith('/api/'):
                return jsonify({'error': 'Authentication required'}), 401
            # For regular pages, redirect to login
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def get_last_updated(file_paths=None):
    if file_paths is None:
        file_paths = []
    
    if isinstance(file_paths, str):
        file_paths = [file_paths]
    
    try:
        latest_time = None
        for file_path in file_paths:
            if os.path.exists(file_path):
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if latest_time is None or mod_time > latest_time:
                    latest_time = mod_time
        
        if latest_time is None:
            return "No files found"

        return latest_time.strftime("%Y-%m-%d %H:%M:%S")
    except Exception:
        return "Unknown"

def get_week_number(date):
    today = datetime.now()
    days_since_wednesday = (today.weekday() - 2) % 7
    last_reset = today - timedelta(days=days_since_wednesday)
    return date >= last_reset

@app.route('/')
def index():
    try:
        df = pd.read_csv('PS_version/characters.csv')

        url_map = {}
        try:
            registers_df = pd.read_csv('PS_version/csv_registers.csv')
            for _, row in registers_df.iterrows():
                url = row['url']
                if url and 'character' in url:
                    if url.strip() and url.strip().split('/')[-1]:
                        char_name = url.strip().split('/')[-1].capitalize()
                        url_map[char_name] = url.strip()
        except Exception:
            pass

        tier_map = {}
        try:
            tier_df = pd.read_csv('PS_version/csv_tier_pieces.csv')
            for _, row in tier_df.iterrows():
                tier_map[row['name']] = int(row['tier_Pieces'])
        except Exception:
            pass

        # Load cloak data from detailed export
        cloak_map = {}
        try:
            export_df = pd.read_csv('PS_version/csv_export.csv')
            for _, row in export_df.iterrows():
                cloak_map[row['Charname']] = {
                    'back_rank': row.get('Back_rank', '-'),
                    'back_socket_type': row.get('Back_socket_type', '-')
                }
        except Exception:
            pass

        # Load role overrides
        override_map = {}
        try:
            override_df = pd.read_csv('PS_version/csv_role_overrides.csv')
            for _, row in override_df.iterrows():
                key = f"{row['character_name'].lower()}_{row['realm'].lower()}"
                override_map[key] = {
                    'role': row['override_role'],
                    'spec': row['override_spec']
                }
        except Exception:
            pass

        # Load raid token data
        raid_token_map = {}
        try:
            raid_token_df = pd.read_csv('PS_version/csv_raid_tokens.csv')
            for _, row in raid_token_df.iterrows():
                key = f"{row['character_name'].lower()}_{row['realm'].lower()}"
                raid_token_map[key] = {
                    'raid_token_1': row['raid_token_1'] if pd.notna(row['raid_token_1']) else None,
                    'raid_token_2': row['raid_token_2'] if pd.notna(row['raid_token_2']) else None
                }
        except Exception:
            pass

        characters = []
        for _, row in df.iterrows():
            # Check for role/spec overrides
            override_key = f"{row['name'].lower()}_{row['realm'].lower()}"
            override_data = override_map.get(override_key)
            raid_token_data = raid_token_map.get(override_key)

            cloak_data = cloak_map.get(row['name'], {'back_rank': '-', 'back_socket_type': '-'})

            # Extract rank number for display
            rank_number = 0
            if cloak_data['back_rank'] and cloak_data['back_rank'] != '-':
                try:
                    # Extract number from "Rank X" format
                    rank_number = int(cloak_data['back_rank'].split(' ')[-1])
                except (ValueError, IndexError):
                    rank_number = 0

            character = {
                'name': row['name'],
                'ilvl': row['ilvl'],
                'realm': row['realm'],
                'role': override_data['role'] if override_data else row['role'],
                'class_name': row['class_name'],
                'spec': override_data['spec'] if override_data else row['spec'],
                'armor_type': row['armor_type'],
                'tier_token': row['tier_token'],
                'tier_pieces': tier_map.get(row['name'], 0),
                'url': url_map.get(row['name'], '#'),
                'has_override': override_data is not None,
                'original_role': row['role'] if override_data else None,
                'original_spec': row['spec'] if override_data else None,
                'raid_token_1': raid_token_data['raid_token_1'] if raid_token_data else None,
                'raid_token_2': raid_token_data['raid_token_2'] if raid_token_data else None,
                'back_rank': cloak_data['back_rank'],
                'back_rank_number': rank_number,
                'back_socket_type': cloak_data['back_socket_type'],
                'has_pure_fiber': cloak_data['back_socket_type'] == 'Pure Fiber'
            }
            characters.append(character)

        # Sort characters by role: Tank -> Healer -> Melee DPS -> Ranged DPS
        role_priority = {
            'Tank': 1,
            'Healer': 2,
            'Melee DPS': 3,
            'Melee': 3,  # Handle both "Melee DPS" and "Melee" variants
            'Ranged DPS': 4,
            'Ranged': 4  # Handle both "Ranged DPS" and "Ranged" variants
        }

        characters.sort(key=lambda x: (role_priority.get(x['role'], 5), x['name']))

        # Check if user is authenticated for edit capabilities
        is_authenticated = session.get('authenticated', False)

        return render_template('index.html', characters=characters, is_authenticated=is_authenticated,
                              last_updated=get_last_updated(['PS_version/characters.csv', 'PS_version/csv_registers.csv', 'PS_version/csv_tier_pieces.csv', 'PS_version/csv_export.csv']))
    except Exception:
        return render_template('index.html', characters=[], is_authenticated=False,
                              last_updated=get_last_updated(['PS_version/characters.csv', 'PS_version/csv_registers.csv', 'PS_version/csv_tier_pieces.csv', 'PS_version/csv_export.csv']))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        config = load_config()
        if request.form.get('password') == config['app_settings']['password']:
            session['authenticated'] = True
            return redirect(url_for('raiders'))
        return render_template('login.html', error='Invalid password')
    return render_template('login.html')

@app.route('/raiders')
@requires_password
def raiders():
    try:
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})
        
        discord_roles = {}
        for _, row in discord_df.iterrows():
            user_id = str(row['ID'])
            is_raider = str(row['Raider']).lower() == 'true'
            discord_roles[user_id] = 'Assigned' if is_raider else 'Not assigned'
        
        armory_urls = []
        for _, row in df.iterrows():
            discord_id = str(row.get('discord_id', ''))
            role = discord_roles.get(discord_id, 'Not assigned')
            armory_urls.append((row['url'], row['discord'], row['status'], role))
        
        return render_template('raiders.html', armory_urls=armory_urls,
                              last_updated=get_last_updated(['PS_version/csv_registers.csv', 'discord_exports/csv_user_export.csv']))
    except Exception:
        return render_template('raiders.html', armory_urls=[],
                              last_updated=get_last_updated(['PS_version/csv_registers.csv', 'discord_exports/csv_user_export.csv']))

@app.route('/favicon.ico')
def favicon():
    return send_from_directory('static', 'favicon.ico')

@app.route('/add_url', methods=['POST'])
def add_url():
    data = request.get_json()
    if not data or not all(k in data for k in ['url', 'discord', 'discord_id']):
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        df = pd.read_csv('PS_version/csv_registers.csv')
        
        new_row = {
            'url': data['url'],
            'discord': data['discord'],
            'discord_id': data['discord_id'],
            'status': 'pending'
        }
        
        df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
        df.to_csv('PS_version/csv_registers.csv', index=False)
        
        return jsonify({'message': 'URL added successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/remove_url', methods=['POST'])
def remove_url():
    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({'error': 'No URL provided'}), 400

    try:
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})
        
        mask = df['url'] == data['url']
        if not mask.any():
            return jsonify({'error': 'URL not found'}), 404
            
        discord_id = str(df.loc[mask, 'discord_id'].iloc[0])
        discord_user = discord_df[discord_df['ID'].astype(str) == discord_id]
        
        if not discord_user.empty:
            roles = discord_user['Roles'].iloc[0]
            
            if pd.notna(roles) and 'Uproar Raider' in roles:
                if remove_discord_role(discord_id):
                    role_index = discord_df[discord_df['ID'].astype(str) == discord_id].index[0]
                    
                    current_roles = discord_df.at[role_index, 'Roles']
                    if current_roles == 'Uproar Raider':
                        discord_df.at[role_index, 'Roles'] = ''
                    else:
                        discord_df.at[role_index, 'Roles'] = current_roles.replace(', Uproar Raider', '').replace('Uproar Raider, ', '')
                    
                    discord_df.at[role_index, 'Raider'] = 'False'
                    discord_df.to_csv('discord_exports/csv_user_export.csv', index=False)
                else:
                    return jsonify({'error': 'Failed to remove Discord role'}), 500
        
        df.loc[mask, 'status'] = 'inactive'
        df.to_csv('PS_version/csv_registers.csv', index=False)
        
        return jsonify({'message': 'Raider status updated to inactive'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/update_data', methods=['POST'])
def update_data():
    try:
        if not SUDO_PASSWORD:
            return jsonify({'error': 'Sudo password not configured'}), 500

        command = "docker exec -w /data powershell-container pwsh -file PS_version/Main.ps1"
        command_args = command.split()

        process = subprocess.Popen(['sudo', '-S'] + command_args,
                                 stdin=subprocess.PIPE,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 universal_newlines=True)

        stdout, stderr = process.communicate(input=f"{SUDO_PASSWORD}\n")

        if process.returncode != 0:
            return jsonify({'error': f'Failed to update data: {stderr}'}), 500

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'message': 'Data update completed successfully',
            'timestamp': timestamp,
            'output': stdout
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/approve_url', methods=['POST'])
def approve_url():
    data = request.get_json()
    url = data.get('url')

    if not url:
        return jsonify({'error': 'No URL provided'}), 400

    try:
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})

        mask = df['url'] == url
        if not mask.any():
            return jsonify({'error': 'URL not found'}), 404

        discord_id = str(df.loc[mask, 'discord_id'].iloc[0])
        discord_user = discord_df[discord_df['ID'].astype(str) == discord_id]

        if not discord_user.empty:
            roles = discord_user['Roles'].iloc[0]

            if pd.isna(roles) or 'Uproar Raider' not in roles:
                if assign_discord_role(discord_id):
                    role_index = discord_df[discord_df['ID'].astype(str) == discord_id].index[0]

                    current_roles = discord_df.at[role_index, 'Roles']
                    if pd.isna(current_roles):
                        discord_df.at[role_index, 'Roles'] = 'Uproar Raider'
                    else:
                        discord_df.at[role_index, 'Roles'] = f"{current_roles}, Uproar Raider"

                    discord_df.at[role_index, 'Raider'] = 'True'
                    discord_df.to_csv('discord_exports/csv_user_export.csv', index=False)
                else:
                    return jsonify({'error': 'Failed to assign Discord role'}), 500
        else:
            return jsonify({'error': f'Discord user not found: {discord_id}'}), 404

        df.loc[mask, 'status'] = 'approved'
        df.to_csv('PS_version/csv_registers.csv', index=False)

        return jsonify({'message': 'URL approved successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/reactivate_url', methods=['POST'])
def reactivate_url():
    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({'error': 'No URL provided'}), 400

    try:
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})

        mask = df['url'] == data['url']
        if not mask.any():
            return jsonify({'error': 'URL not found'}), 404

        discord_id = str(df.loc[mask, 'discord_id'].iloc[0])
        discord_user = discord_df[discord_df['ID'].astype(str) == discord_id]

        if not discord_user.empty:
            roles = discord_user['Roles'].iloc[0]

            if pd.isna(roles) or 'Uproar Raider' not in roles:
                if assign_discord_role(discord_id):
                    role_index = discord_df[discord_df['ID'].astype(str) == discord_id].index[0]

                    current_roles = discord_df.at[role_index, 'Roles']
                    if pd.isna(current_roles):
                        discord_df.at[role_index, 'Roles'] = 'Uproar Raider'
                    else:
                        discord_df.at[role_index, 'Roles'] = f"{current_roles}, Uproar Raider"

                    discord_df.at[role_index, 'Raider'] = 'True'
                    discord_df.to_csv('discord_exports/csv_user_export.csv', index=False)
                else:
                    return jsonify({'error': 'Failed to assign Discord role'}), 500
        else:
            return jsonify({'error': f'Discord user not found: {discord_id}'}), 404

        df.loc[mask, 'status'] = 'approved'
        df.to_csv('PS_version/csv_registers.csv', index=False)

        return jsonify({'message': 'Raider reactivated successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/update_discord_ids', methods=['POST'])
def update_discord_ids():
    try:
        registers_df = pd.read_csv('PS_version/csv_registers.csv')
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})

        discord_id_map = {}
        for _, row in discord_df.iterrows():
            full_username = row['Username']
            base_username = full_username.split('#')[0] if '#' in full_username else full_username
            base_username_no_dots = base_username.replace('.', '')
            discord_id_map[full_username] = str(row['ID'])
            discord_id_map[base_username] = str(row['ID'])
            discord_id_map[base_username_no_dots] = str(row['ID'])

        registers_df['discord_id'] = registers_df['discord'].map(discord_id_map)
        registers_df.to_csv('PS_version/csv_registers.csv', index=False)

        return jsonify({'message': 'Discord IDs updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/tier')
def tier():
    try:
        selected_date = request.args.get('date', 'current')

        if selected_date == 'current':
            file_path = 'PS_version/csv_tier_pieces.csv'
        else:
            file_path = f'PS_version/Archive/Tier/{selected_date}_csv_tier_pieces.csv'

        df = pd.read_csv(file_path)

        characters = []
        for _, row in df.iterrows():
            character = {
                'name': row['name'],
                'realm': row['realm'],
                'token': row['token'],
                'head': int(row['Head']),
                'shoulders': int(row['Shoulders']),
                'chest': int(row['Chest']),
                'hands': int(row['Hands']),
                'legs': int(row['Legs'])
            }
            characters.append(character)

        return render_template('tier.html', characters=characters,
                              last_updated=get_last_updated(file_path), selected_date=selected_date)
    except Exception:
        return render_template('tier.html', characters=[],
                              last_updated=get_last_updated('PS_version/csv_tier_pieces.csv'), selected_date='current')

@app.route('/get_historical_dates')
def get_historical_dates():
    try:
        archive_dir = 'PS_version/Archive/Tier'
        files = [f for f in os.listdir(archive_dir) if f.endswith('_csv_tier_pieces.csv')]
        dates = [f.split('_')[0] for f in files]
        dates.sort(reverse=True)
        return jsonify({'dates': dates})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/enchants')
def enchants():
    try:
        selected_date = request.args.get('date', 'current')

        if selected_date == 'current':
            file_path = 'PS_version/csv_enchants.csv'
        else:
            file_path = f'PS_version/Archive/Enchants/{selected_date}_csv_enchants.csv'

        df = pd.read_csv(file_path)

        characters = []
        for _, row in df.iterrows():
            character = {
                'name': row['name'],
                'realm': row['realm'],
                'Head': row['Head'],
                'Head_tier': row.get('Head_tier', 0),
                'Back': row['Back'],
                'Back_tier': row.get('Back_tier', 0),
                'Chest': row['Chest'],
                'Chest_tier': row.get('Chest_tier', 0),
                'wrist': row['wrist'],
                'wrist_tier': row.get('wrist_tier', 0),
                'Legs': row['Legs'],
                'Legs_tier': row.get('Legs_tier', 0),
                'feet': row['feet'],
                'feet_tier': row.get('feet_tier', 0),
                'finger_1': row['finger_1'],
                'finger_1_tier': row.get('finger_1_tier', 0),
                'finger_2': row['finger_2'],
                'finger_2_tier': row.get('finger_2_tier', 0),
                'Main_hand': row['Main_hand'],
                'Main_hand_tier': row.get('Main_hand_tier', 0)
            }
            characters.append(character)

        return render_template('enchants.html', characters=characters,
                              last_updated=get_last_updated(file_path), selected_date=selected_date)
    except Exception:
        return render_template('enchants.html', characters=[],
                              last_updated=get_last_updated('PS_version/csv_enchants.csv'), selected_date='current')

@app.route('/get_historical_enchant_dates')
def get_historical_enchant_dates():
    try:
        archive_dir = 'PS_version/Archive/Enchants'
        if not os.path.exists(archive_dir):
            return jsonify({'dates': []})

        files = [f for f in os.listdir(archive_dir) if f.endswith('_csv_enchants.csv')]
        dates = [f.split('_')[0] for f in files]
        dates.sort(reverse=True)

        return jsonify({'dates': dates})
    except Exception:
        return jsonify({'dates': []})

@app.route('/logs')
def logs():
    try:
        csv_path = 'PS_version/csv_logs.csv'
        if not os.path.exists(csv_path):
            sample_data = pd.DataFrame({
                'Date': ['2023-10-15', '2023-10-15', '2023-10-16'],
                'Encounter': ['Gnarlroot', 'Igira the Cruel', 'Council of Dreams'],
                'Duration': ['5:23', '7:45', '8:12'],
                'Kill': ['True', 'False', 'True'],
                'Owner': ['Seeddy', 'Frosty', 'Healbot'],
                'Link': ['https://www.warcraftlogs.com/reports/sample1',
                         'https://www.warcraftlogs.com/reports/sample2',
                         'https://www.warcraftlogs.com/reports/sample3'],
                'Zone': ['Amirdrassil', 'Amirdrassil', 'Amirdrassil']
            })
            os.makedirs(os.path.dirname(csv_path), exist_ok=True)
            sample_data.to_csv(csv_path, index=False)

        df = pd.read_csv(csv_path)

        required_columns = ['Date', 'Encounter', 'Duration', 'Kill', 'Owner', 'Link', 'Zone']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            for col in missing_columns:
                df[col] = 'Unknown'

        if 'Encounter' in df.columns:
            df = df[~df['Encounter'].str.contains(r'\+', regex=True, na=False)]

        # Convert Date column to datetime for proper sorting
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
            # Sort by date (most recent first)
            df = df.sort_values('Date', ascending=False)
            # Convert back to string for display
            df['Date'] = df['Date'].dt.strftime('%Y-%m-%d')

        zones = sorted(df['Zone'].unique()) if 'Zone' in df.columns else []

        # Set default values: kills only and hide duplicates
        selected_zone = request.args.get('zone', '')
        selected_kill = request.args.get('kill', 'true')  # Default to kills only
        show_duplicates = request.args.get('show_duplicates', 'false').lower() == 'true'

        # Apply zone filter
        if selected_zone and selected_zone != '':
            df = df[df['Zone'] == selected_zone]

        # Apply kill filter - handle both string and boolean values
        if selected_kill and selected_kill != '' and selected_kill.lower() != 'all':
            if selected_kill.lower() == 'true':
                # Show only kills
                df = df[df['Kill'].astype(str).str.lower().isin(['true', '1', 'yes'])]
            elif selected_kill.lower() == 'false':
                # Show only wipes
                df = df[df['Kill'].astype(str).str.lower().isin(['false', '0', 'no'])]
            # If selected_kill is 'all', show all results (no filter applied)

        # Apply duplicate filter - default to hiding duplicates
        if not show_duplicates:
            df = df.drop_duplicates(subset=['Encounter'], keep='first')

        logs = []
        for _, row in df.iterrows():
            # Convert kill value to boolean for template
            kill_value = str(row['Kill']).lower() in ['true', '1', 'yes']
            log = {
                'date': row['Date'],
                'encounter': row['Encounter'],
                'duration': row['Duration'],
                'kill': kill_value,
                'owner': row['Owner'],
                'link': row['Link'],
                'zone': row['Zone']
            }
            logs.append(log)

        return render_template('logs.html', logs=logs, zones=zones,
                              selected_zone=selected_zone,
                              selected_kill=selected_kill,
                              show_duplicates=show_duplicates,
                              last_updated=get_last_updated('PS_version/csv_logs.csv'))
    except Exception:
        return render_template('logs.html', logs=[], zones=[],
                              selected_zone='',
                              selected_kill='true',  # Default to kills only
                              show_duplicates=False,
                              last_updated=get_last_updated('PS_version/csv_logs.csv'))

@app.route('/fetch-logs', methods=['POST'])
def fetch_logs():
    try:
        if not SUDO_PASSWORD:
            return jsonify({'error': 'Sudo password not configured'}), 500

        command = "docker exec -w /data powershell-container pwsh -file PS_version/get_logs.ps1"
        command_args = command.split()

        process = subprocess.Popen(['sudo', '-S'] + command_args,
                                 stdin=subprocess.PIPE,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 universal_newlines=True)

        stdout, stderr = process.communicate(input=f"{SUDO_PASSWORD}\n")

        if process.returncode != 0:
            return jsonify({'error': f'Failed to fetch logs: {stderr}'}), 500

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'message': 'Logs fetched successfully',
            'timestamp': timestamp,
            'output': stdout
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/inspect')
def inspect():
    try:
        # Load detailed character data from csv_export.csv
        export_path = 'PS_version/csv_export.csv'
        if not os.path.exists(export_path):
            return render_template('inspect.html', character=None, characters=[], error="Character data not found")

        df = pd.read_csv(export_path)

        # Get list of all characters for dropdown
        characters_list = sorted(df['Charname'].tolist())

        character_name = request.args.get('character')
        if not character_name:
            return render_template('inspect.html', character=None, characters=characters_list, error=None)

        character_data = df[df['Charname'].str.lower() == character_name.lower()]

        if character_data.empty:
            return render_template('inspect.html', character=None, characters=characters_list, error=f"Character '{character_name}' not found")

        row = character_data.iloc[0]

        # Function to extract item level from gear name
        def extract_item_level(gear_name):
            if pd.isna(gear_name) or gear_name == '' or gear_name == '-':
                return 0
            # Extract number in parentheses, e.g., "Item Name (675)" -> 675
            import re
            match = re.search(r'\((\d+)\)', str(gear_name))
            return int(match.group(1)) if match else 0

        # Calculate precise item level from equipped gear
        gear_slots = [
            row['Helmet'], row['Neck'], row['Shoulder'], row['Chest'],
            row['Waist'], row['Legs'], row['Feet'], row['Wrist'],
            row['Hands'], row['FINGER_1'], row['FINGER_2'],
            row['TRINKET_1'], row['TRINKET_2'], row['Back'],
            row['MAIN_HAND'], row['OFF_HAND']
        ]

        # Extract item levels and calculate average
        item_levels = []
        for gear in gear_slots:
            ilvl = extract_item_level(gear)
            if ilvl > 0:  # Only count items that have valid item levels
                item_levels.append(ilvl)

        # Calculate precise average item level
        calculated_ilvl = sum(item_levels) / len(item_levels) if item_levels else 0

        # Build character object with all gear information
        character = {
            'name': row['Charname'],
            'realm': row['Realm'],
            'race': row['Race'],
            'class_name': row['Class'],
            'spec': row['Spec'],
            'role': row['Role'],
            'ilvl': row['ilvl'],  # Original from API
            'calculated_ilvl': f"{calculated_ilvl:.2f}",  # Calculated from gear
            'armor_type': row['Armor_type'],
            'tier_token': row['Tier_Token'],
            'faction': row['Faction'],
            'tier_pieces': row['Tier_Pieces'],
            'rating': row['Rating'],
            'embellishments': row['embellishments'],
            'gear': {
                'head': {'name': row['Helmet'], 'tier': row['Helmet_tier'], 'socket': row['helmet_socket'], 'gem': row['helmet_socket_name'], 'enchant': row['helmet_enchant']},
                'neck': {'name': row['Neck'], 'sockets': row['neck_sockets'], 'gem1': row['neck_gem1'], 'gem2': row['neck_gem2']},
                'shoulders': {'name': row['Shoulder'], 'tier': row['Shoulder_tier'], 'socket': row['Shoulders_sock'], 'gem': row['Shoulder_gem']},
                'chest': {'name': row['Chest'], 'tier': row['Chest_tier'], 'socket': row['chest_sock'], 'gem': row['chest_gem'], 'enchant': row['chest_enchant']},
                'waist': {'name': row['Waist'], 'socket': row['Waist_sock'], 'gem': row['Waist_gem']},
                'legs': {'name': row['Legs'], 'tier': row['Legs_tier'], 'socket': row['Legs_sock'], 'gem': row['Legs_gem'], 'enchant': row['Legs_enchant']},
                'feet': {'name': row['Feet'], 'socket': row['Feet_sock'], 'gem': row['Feet_gem'], 'enchant': row['Feet_enchant']},
                'wrists': {'name': row['Wrist'], 'socket': row['Wrist_sock'], 'gem': row['Wrist_gem'], 'enchant': row['Wrist_enchant']},
                'hands': {'name': row['Hands'], 'tier': row['Hands_tier'], 'socket': row['Hands_sock'], 'gem': row['Hands_gem'], 'enchant': row['Hands_enchant']},
                'finger1': {'name': row['FINGER_1'], 'sockets': row['FINGER_1_sockets'], 'gem1': row['FINGER_1_gem1'], 'gem2': row['FINGER_1_gem2'], 'enchant': row['FINGER_1_enchant']},
                'finger2': {'name': row['FINGER_2'], 'sockets': row['FINGER_2_sockets'], 'gem1': row['FINGER_2_gem1'], 'gem2': row['FINGER_2_gem2'], 'enchant': row['FINGER_2_enchant']},
                'trinket1': {'name': row['TRINKET_1']},
                'trinket2': {'name': row['TRINKET_2']},
                'back': {'name': row['Back'], 'rank': row.get('Back_rank', '-'), 'socket': row['Back_sock'], 'gem': row['Back_gem'], 'socket_type': row.get('Back_socket_type', '-'), 'enchant': row['Back_enchant']},
                'main_hand': {'name': row['MAIN_HAND'], 'enchant': row['MAIN_HAND_ench']},
                'off_hand': {'name': row['OFF_HAND'], 'enchant': row['OFF_HAND_ench']}
            },
            'currencies': {
                'valor': row['Valor'],
                'weathered_crests': row['Weathered Harbinger Crests'],
                'carved_crests': row['Carved Harbinger Crests'],
                'runed_crests': row['Runed Harbinger Crests'],
                'gilded_crests': row['Gilded Harbinger Crests']
            }
        }

        # Check if character image exists - try multiple variations
        import urllib.parse

        # Get list of actual image files
        images_dir = "PS_version/images"
        if os.path.exists(images_dir):
            actual_images = [f.lower() for f in os.listdir(images_dir) if f.endswith('.jpg')]
        else:
            actual_images = []

        # Try different variations of the character name
        name_variations = [
            character['name'].lower(),  # drexyl
            character['name'],          # Drexyl
            urllib.parse.quote(character['name'].lower()),  # handle special chars
            urllib.parse.quote(character['name'])           # handle special chars with case
        ]

        character['has_image'] = False
        character['image_path'] = None

        for name_var in name_variations:
            test_filename = f"{name_var}.jpg"
            if test_filename.lower() in actual_images:
                # Find the actual filename with correct case
                for actual_file in os.listdir(images_dir):
                    if actual_file.lower() == test_filename.lower():
                        character['has_image'] = True
                        character['image_path'] = f"images/{actual_file}"
                        break
                break

        return render_template('inspect.html', character=character, characters=characters_list, error=None)

    except Exception as e:
        # Try to load characters list even if there's an error
        try:
            export_path = 'PS_version/csv_export.csv'
            if os.path.exists(export_path):
                df = pd.read_csv(export_path)
                characters_list = sorted(df['Charname'].tolist())
            else:
                characters_list = []
        except:
            characters_list = []
        return render_template('inspect.html', character=None, characters=characters_list, error=f"Error loading character data: {str(e)}")

@app.route('/images/<path:filename>')
def character_images(filename):
    """Serve character images from PS_version/images directory"""
    return send_from_directory('PS_version/images', filename)

@app.route('/progress')
def progress():
    try:
        selected_week = request.args.get('week', 'current')

        characters_path = 'PS_version/characters.csv'
        dungeon_details_path = 'PS_version/all_dungeon_details.csv'

        if not os.path.exists(characters_path):
            return render_template('progress.html', characters=[], weeks=[],
                                  last_updated=get_last_updated([characters_path, dungeon_details_path]),
                                  error=f"Characters file not found")

        if not os.path.exists(dungeon_details_path):
            return render_template('progress.html', characters=[], weeks=[],
                                  last_updated=get_last_updated([characters_path, dungeon_details_path]),
                                  error=f"Dungeon details file not found")

        characters_df = pd.read_csv(characters_path)
        dungeon_details_df = pd.read_csv(dungeon_details_path)

        dungeon_details_df['Date'] = pd.to_datetime(dungeon_details_df['Date'], format='mixed', errors='coerce')
        dungeon_details_df['Week'] = dungeon_details_df['Date'].apply(get_week_number)

        all_weeks = sorted(dungeon_details_df['Week'].unique())

        filter_week = None
        if selected_week != 'current':
            try:
                filter_week = int(selected_week)
            except ValueError:
                filter_week = get_week_number(datetime.now())
        else:
            today = datetime.now()
            filter_week = get_week_number(today)

        characters_data = []
        for _, character_row in characters_df.iterrows():
            character_name = character_row['name']

            character_runs = dungeon_details_df[dungeon_details_df['Character'] == character_name]
            total_runs = len(character_runs)

            week_runs = character_runs[character_runs['Week'] == filter_week]
            week_runs_count = len(week_runs)

            if week_runs_count > 0:
                highest_key = week_runs['Key Level'].max()
            else:
                highest_key = 0

            characters_data.append({
                'name': character_name,
                'week_number': filter_week,
                'total_runs': total_runs,
                'this_week': week_runs_count,
                'highest': highest_key
            })

        return render_template('progress.html',
                              characters=characters_data,
                              weeks=all_weeks,
                              last_updated=get_last_updated([characters_path, dungeon_details_path]),
                              selected_week=selected_week)
    except Exception:
        return render_template('progress.html', characters=[], weeks=[],
                              last_updated=get_last_updated(['PS_version/characters.csv', 'PS_version/all_dungeon_details.csv']))

# Role Override Management Endpoints
@app.route('/api/role_overrides', methods=['GET'])
@requires_password
def get_role_overrides():
    """Get all role overrides"""
    try:
        df = pd.read_csv('PS_version/csv_role_overrides.csv')
        overrides = df.to_dict('records')
        return jsonify({'overrides': overrides})
    except FileNotFoundError:
        return jsonify({'overrides': []})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/role_overrides', methods=['POST'])
@requires_password
def set_role_override():
    """Set or update a role override"""
    data = request.get_json()
    if not data or not all(k in data for k in ['character_name', 'realm', 'override_role', 'override_spec']):
        return jsonify({'error': 'Missing required fields: character_name, realm, override_role, override_spec'}), 400

    try:
        # Load existing overrides
        try:
            df = pd.read_csv('PS_version/csv_role_overrides.csv')
        except FileNotFoundError:
            df = pd.DataFrame(columns=['character_name', 'realm', 'override_role', 'override_spec', 'created_by', 'created_at', 'updated_at'])

        character_name = data['character_name'].strip()
        realm = data['realm'].strip()
        override_role = data['override_role'].strip()
        override_spec = data['override_spec'].strip()
        created_by = session.get('authenticated_user', 'admin')
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Check if override already exists
        mask = (df['character_name'].str.lower() == character_name.lower()) & (df['realm'].str.lower() == realm.lower())

        if mask.any():
            # Update existing override
            df.loc[mask, 'override_role'] = override_role
            df.loc[mask, 'override_spec'] = override_spec
            df.loc[mask, 'updated_at'] = current_time
        else:
            # Create new override
            new_row = {
                'character_name': character_name,
                'realm': realm,
                'override_role': override_role,
                'override_spec': override_spec,
                'created_by': created_by,
                'created_at': current_time,
                'updated_at': current_time
            }
            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)

        df.to_csv('PS_version/csv_role_overrides.csv', index=False)
        return jsonify({'message': 'Role override saved successfully'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/role_overrides/<character_name>/<realm>', methods=['DELETE'])
@requires_password
def delete_role_override(character_name, realm):
    """Delete a role override"""
    try:
        df = pd.read_csv('PS_version/csv_role_overrides.csv')

        # Find and remove the override
        mask = (df['character_name'].str.lower() == character_name.lower()) & (df['realm'].str.lower() == realm.lower())

        if not mask.any():
            return jsonify({'error': 'Override not found'}), 404

        df = df[~mask]
        df.to_csv('PS_version/csv_role_overrides.csv', index=False)

        return jsonify({'message': 'Role override deleted successfully'})

    except FileNotFoundError:
        return jsonify({'error': 'Override not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Combined Character Data Management Endpoint
@app.route('/api/character_data', methods=['POST'])
@requires_password
def set_character_data():
    """Set or update character data including role overrides and raid tokens"""
    data = request.get_json()
    if not data or not all(k in data for k in ['character_name', 'realm', 'override_role', 'override_spec']):
        return jsonify({'error': 'Missing required fields: character_name, realm, override_role, override_spec'}), 400

    try:
        character_name = data['character_name'].strip()
        realm = data['realm'].strip()
        override_role = data['override_role'].strip()
        override_spec = data['override_spec'].strip()
        raid_token_1 = data.get('raid_token_1', '').strip() or None
        raid_token_2 = data.get('raid_token_2', '').strip() or None
        created_by = session.get('authenticated_user', 'admin')
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Handle role overrides
        try:
            override_df = pd.read_csv('PS_version/csv_role_overrides.csv')
        except FileNotFoundError:
            override_df = pd.DataFrame(columns=['character_name', 'realm', 'override_role', 'override_spec', 'created_by', 'created_at', 'updated_at'])

        override_mask = (override_df['character_name'].str.lower() == character_name.lower()) & (override_df['realm'].str.lower() == realm.lower())

        if override_mask.any():
            # Update existing override
            override_df.loc[override_mask, 'override_role'] = override_role
            override_df.loc[override_mask, 'override_spec'] = override_spec
            override_df.loc[override_mask, 'updated_at'] = current_time
        else:
            # Create new override
            new_override = {
                'character_name': character_name,
                'realm': realm,
                'override_role': override_role,
                'override_spec': override_spec,
                'created_by': created_by,
                'created_at': current_time,
                'updated_at': current_time
            }
            override_df = pd.concat([override_df, pd.DataFrame([new_override])], ignore_index=True)

        override_df.to_csv('PS_version/csv_role_overrides.csv', index=False)

        # Handle raid tokens
        try:
            raid_token_df = pd.read_csv('PS_version/csv_raid_tokens.csv')
        except FileNotFoundError:
            raid_token_df = pd.DataFrame(columns=['character_name', 'realm', 'raid_token_1', 'raid_token_2', 'created_by', 'created_at', 'updated_at'])

        raid_token_mask = (raid_token_df['character_name'].str.lower() == character_name.lower()) & (raid_token_df['realm'].str.lower() == realm.lower())

        if raid_token_mask.any():
            # Update existing raid token data
            raid_token_df.loc[raid_token_mask, 'raid_token_1'] = raid_token_1
            raid_token_df.loc[raid_token_mask, 'raid_token_2'] = raid_token_2
            raid_token_df.loc[raid_token_mask, 'updated_at'] = current_time
        else:
            # Create new raid token data
            new_raid_token = {
                'character_name': character_name,
                'realm': realm,
                'raid_token_1': raid_token_1,
                'raid_token_2': raid_token_2,
                'created_by': created_by,
                'created_at': current_time,
                'updated_at': current_time
            }
            raid_token_df = pd.concat([raid_token_df, pd.DataFrame([new_raid_token])], ignore_index=True)

        raid_token_df.to_csv('PS_version/csv_raid_tokens.csv', index=False)
        return jsonify({'message': 'Character data saved successfully'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Raid Token Management Endpoints
@app.route('/api/raid_tokens', methods=['GET'])
@requires_password
def get_raid_tokens():
    """Get all raid token data"""
    try:
        df = pd.read_csv('PS_version/csv_raid_tokens.csv')
        tokens = df.to_dict('records')
        return jsonify({'raid_tokens': tokens})
    except FileNotFoundError:
        return jsonify({'raid_tokens': []})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Guide Routes
@app.route('/guide')
def guide():
    """Display the 11.2 guide page with tabs"""
    try:
        tabs, content = load_guide_data()

        # Sort tabs by order
        tabs = sorted(tabs, key=lambda x: x['order'])

        # Get selected tab (default to first tab)
        selected_tab_id = request.args.get('tab', str(tabs[0]['id']) if tabs else '1')
        selected_tab_id = int(selected_tab_id)

        # Get content for selected tab
        selected_content = content.get(selected_tab_id, '')

        # Check if user is authenticated for edit capabilities
        is_authenticated = session.get('authenticated', False)

        return render_template('guide.html',
                             tabs=tabs,
                             selected_tab_id=selected_tab_id,
                             selected_content=selected_content,
                             is_authenticated=is_authenticated,
                             last_updated=get_last_updated(['PS_version/csv_guide_tabs.csv', 'PS_version/csv_guide_content.csv']))
    except Exception as e:
        return render_template('guide.html',
                             tabs=[],
                             selected_tab_id=1,
                             selected_content='Error loading guide content.',
                             is_authenticated=False,
                             error=str(e),
                             last_updated=get_last_updated(['PS_version/csv_guide_tabs.csv', 'PS_version/csv_guide_content.csv']))

@app.route('/api/guide/tabs', methods=['POST'])
@requires_password
def create_or_update_tab():
    """Create or update a guide tab"""
    try:
        data = request.get_json()
        if not data or 'title' not in data:
            return jsonify({'error': 'Title is required'}), 400

        # Set default order if not provided
        if 'order' not in data:
            tabs, _ = load_guide_data()
            data['order'] = len(tabs) + 1

        success = save_guide_tab(data)
        if success:
            return jsonify({'message': 'Tab saved successfully'})
        else:
            return jsonify({'error': 'Failed to save tab'}), 500
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/guide/tabs/<int:tab_id>', methods=['DELETE'])
@requires_password
def delete_tab(tab_id):
    """Delete a guide tab"""
    try:
        success = delete_guide_tab(tab_id)
        if success:
            return jsonify({'message': 'Tab deleted successfully'})
        else:
            return jsonify({'error': 'Failed to delete tab'}), 500
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/guide/content', methods=['POST'])
@requires_password
def update_content():
    """Update content for a specific tab"""
    try:
        data = request.get_json()
        if not data or 'tab_id' not in data or 'content' not in data:
            return jsonify({'error': 'Tab ID and content are required'}), 400

        success = save_guide_content(int(data['tab_id']), data['content'])
        if success:
            return jsonify({'message': 'Content saved successfully'})
        else:
            return jsonify({'error': 'Failed to save content'}), 500
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/config', methods=['GET', 'POST'])
@requires_password
def config_page():
    if request.method == 'POST':
        try:
            # Load current config
            current_config = load_config()

            # Update configuration with form data
            current_config['discord']['raider_role_id'] = request.form.get('raider_role_id')
            current_config['discord']['guild_id'] = request.form.get('guild_id')
            current_config['discord']['support_role_id'] = request.form.get('support_role_id')
            current_config['discord']['ticket_category_id'] = request.form.get('ticket_category_id')

            current_config['warcraft_logs']['client_id'] = request.form.get('wl_client_id')
            current_config['warcraft_logs']['client_secret'] = request.form.get('wl_client_secret')
            current_config['warcraft_logs']['guild_id'] = int(request.form.get('wl_guild_id'))

            current_config['game_settings']['tier_ilvl'] = int(request.form.get('tier_ilvl'))
            current_config['game_settings']['ticket_cooldown'] = int(request.form.get('ticket_cooldown'))
            current_config['game_settings']['max_tickets_per_user'] = int(request.form.get('max_tickets_per_user'))
            current_config['game_settings']['weathered_crest_id'] = int(request.form.get('weathered_crest_id'))
            current_config['game_settings']['carved_crest_id'] = int(request.form.get('carved_crest_id'))
            current_config['game_settings']['runed_crest_id'] = int(request.form.get('runed_crest_id'))
            current_config['game_settings']['gilded_crest_id'] = int(request.form.get('gilded_crest_id'))

            current_config['app_settings']['password'] = request.form.get('app_password')
            current_config['app_settings']['host'] = request.form.get('host')
            current_config['app_settings']['port'] = int(request.form.get('port'))
            current_config['app_settings']['debug'] = 'debug' in request.form

            # Save updated configuration
            save_config(current_config)

            # Update global variables
            global DISCORD_RAIDER_ROLE_ID, DISCORD_GUILD_ID
            DISCORD_RAIDER_ROLE_ID = current_config['discord']['raider_role_id']
            DISCORD_GUILD_ID = current_config['discord']['guild_id']

            return render_template('config.html', config=current_config, success=True)

        except Exception as e:
            current_config = load_config()
            return render_template('config.html', config=current_config, error=f'Error saving configuration: {str(e)}')

    # GET request - display current configuration
    current_config = load_config()
    return render_template('config.html', config=current_config)

# Guide Management Functions
def load_guide_data():
    """Load guide tabs and content from CSV files"""
    try:
        # Load tabs
        tabs_df = pd.read_csv('PS_version/csv_guide_tabs.csv')
        tabs = tabs_df.to_dict('records')

        # Load content
        content_df = pd.read_csv('PS_version/csv_guide_content.csv')
        content = {}
        for _, row in content_df.iterrows():
            content[row['tab_id']] = row['content']

        return tabs, content
    except FileNotFoundError:
        # Create default data if files don't exist
        default_tabs = [
            {'id': 1, 'title': 'Getting Started', 'order': 1, 'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
            {'id': 2, 'title': 'Weekly Objectives', 'order': 2, 'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
            {'id': 3, 'title': 'Gear Priorities', 'order': 3, 'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        ]

        default_content = {
            1: "Welcome to patch 11.2! This guide will help you navigate the first week of the new patch.\n\nKey things to focus on:\n- Complete your weekly objectives\n- Prioritize gear upgrades\n- Plan your progression path",
            2: "Weekly objectives for the first week:\n\n1. Complete your weekly Mythic+ dungeons\n2. Clear Normal and Heroic raid\n3. Collect weekly vault rewards\n4. Farm reputation with new factions",
            3: "Gear priority list:\n\n1. Weapon upgrades (highest priority)\n2. Tier set pieces\n3. Trinkets with new effects\n4. High item level accessories\n\nRemember to check stat priorities for your spec!"
        }

        # Create directories if they don't exist
        os.makedirs('PS_version', exist_ok=True)

        # Save default data
        tabs_df = pd.DataFrame(default_tabs)
        tabs_df.to_csv('PS_version/csv_guide_tabs.csv', index=False)

        content_data = [{'tab_id': tab_id, 'content': content} for tab_id, content in default_content.items()]
        content_df = pd.DataFrame(content_data)
        content_df.to_csv('PS_version/csv_guide_content.csv', index=False)

        return default_tabs, default_content

def save_guide_tab(tab_data):
    """Save or update a guide tab"""
    try:
        df = pd.read_csv('PS_version/csv_guide_tabs.csv')
    except FileNotFoundError:
        df = pd.DataFrame(columns=['id', 'title', 'order', 'created_at'])

    if 'id' in tab_data and tab_data['id']:
        # Update existing tab
        df.loc[df['id'] == tab_data['id'], ['title', 'order']] = [tab_data['title'], tab_data['order']]
    else:
        # Create new tab
        new_id = df['id'].max() + 1 if not df.empty else 1
        new_tab = {
            'id': new_id,
            'title': tab_data['title'],
            'order': tab_data['order'],
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        df = pd.concat([df, pd.DataFrame([new_tab])], ignore_index=True)

        # Also create empty content for new tab
        try:
            content_df = pd.read_csv('PS_version/csv_guide_content.csv')
        except FileNotFoundError:
            content_df = pd.DataFrame(columns=['tab_id', 'content'])

        new_content = {'tab_id': new_id, 'content': ''}
        content_df = pd.concat([content_df, pd.DataFrame([new_content])], ignore_index=True)
        content_df.to_csv('PS_version/csv_guide_content.csv', index=False)

    df.to_csv('PS_version/csv_guide_tabs.csv', index=False)
    return True

def save_guide_content(tab_id, content):
    """Save content for a specific tab"""
    try:
        df = pd.read_csv('PS_version/csv_guide_content.csv')
    except FileNotFoundError:
        df = pd.DataFrame(columns=['tab_id', 'content'])

    if tab_id in df['tab_id'].values:
        # Update existing content
        df.loc[df['tab_id'] == tab_id, 'content'] = content
    else:
        # Create new content entry
        new_content = {'tab_id': tab_id, 'content': content}
        df = pd.concat([df, pd.DataFrame([new_content])], ignore_index=True)

    df.to_csv('PS_version/csv_guide_content.csv', index=False)
    return True

def delete_guide_tab(tab_id):
    """Delete a guide tab and its content"""
    try:
        # Delete from tabs
        tabs_df = pd.read_csv('PS_version/csv_guide_tabs.csv')
        tabs_df = tabs_df[tabs_df['id'] != tab_id]
        tabs_df.to_csv('PS_version/csv_guide_tabs.csv', index=False)

        # Delete from content
        content_df = pd.read_csv('PS_version/csv_guide_content.csv')
        content_df = content_df[content_df['tab_id'] != tab_id]
        content_df.to_csv('PS_version/csv_guide_content.csv', index=False)

        return True
    except Exception:
        return False

if __name__ == '__main__':
    config = load_config()
    app.run(debug=config['app_settings']['debug'],
            port=config['app_settings']['port'],
            host=config['app_settings']['host'])
