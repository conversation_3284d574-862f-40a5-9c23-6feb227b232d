{% extends "base.html" %}

{% block title %}Guild Roster - Uproar{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-start align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-users text-accent me-2"></i>
                    Guild Roster
                    
                </h1>
                                    <!-- Role Composition -->
                    <div class="d-flex align-items-center gap-2">
                        <div class="role-mini tank">
                            <i class="fas fa-shield-alt"></i>
                            <span class="role-tank-count">0</span>
                        </div>
                        <div class="role-mini healer">
                            <i class="fas fa-plus"></i>
                            <span class="role-healer-count">0</span>
                        </div>
                        <div class="role-mini melee">
                            <i class="fas fa-fist-raised"></i>
                            <span class="role-melee-count">0</span>
                        </div>
                        <div class="role-mini ranged">
                            <i class="fas fa-magic"></i>
                            <span class="role-ranged-count">0</span>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>





<!-- Character Roster Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Character Roster
                </h6>

                <!-- Center: Stats, Role Composition, and Buffs/Debuffs -->
                <div class="d-flex align-items-center gap-4">
                    <!-- Avg iLvl -->
                    <div class="d-flex align-items-center gap-1">
                        <span class="h6 text-gold mb-0" id="avgIlvl">0</span>
                        <small class="text-secondary">Avg iLvl</small>
                    </div>



                    <!-- Buffs/Debuffs -->
                    <div class="d-flex align-items-center gap-2">
                        <div class="buff-counter buff-druid" data-class="Druid" data-bs-toggle="tooltip" title="Mark of the Wild">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/spell_nature_regeneration.jpg" alt="Mark of the Wild" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-evoker" data-class="Evoker" data-bs-toggle="tooltip" title="Blessing of the Bronze">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_evoker_blessingofthebronze.jpg" alt="Blessing of the Bronze" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-hunter" data-class="Hunter" data-bs-toggle="tooltip" title="Hunter's Mark">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_hunter_snipershot.jpg" alt="Hunter's Mark" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-mage" data-class="Mage" data-bs-toggle="tooltip" title="Arcane Intellect">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_magicalsentry.jpg" alt="Arcane Intellect" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-priest" data-class="Priest" data-bs-toggle="tooltip" title="Power Word: Fortitude">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_wordfortitude.jpg" alt="Power Word: Fortitude" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-shaman" data-class="Shaman" data-bs-toggle="tooltip" title="Skyfury">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/achievement_raidprimalist_windelemental.jpg" alt="Skyfury" class="buff-icon-mini">
                        </div>
                        <div class="buff-counter buff-warrior" data-class="Warrior" data-bs-toggle="tooltip" title="Battle Shout">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_warrior_battleshout.jpg" alt="Battle Shout" class="buff-icon-mini">
                        </div>
                        <div class="debuff-counter debuff-demonhunter" data-class="Demon Hunter" data-bs-toggle="tooltip" title="Chaos Brand">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_demonhunter_empowerwards.jpg" alt="Chaos Brand" class="debuff-icon-mini">
                        </div>
                        <div class="debuff-counter debuff-monk" data-class="Monk" data-bs-toggle="tooltip" title="Mystic Touch">
                            <img src="https://wow.zamimg.com/images/wow/icons/large/ability_monk_palmstrike.jpg" alt="Mystic Touch" class="debuff-icon-mini">
                        </div>
                    </div>
                </div>

                <!-- Right: Search -->
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search characters..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="character-name">
                                    <i class="fas fa-user me-1"></i>Character
                                </th>
                                <th data-sort="ilvl">
                                    <i class="fas fa-star me-1"></i>iLvl
                                </th>
                                <th data-sort="role">
                                    <i class="fas fa-users-cog me-1"></i>Role
                                </th>
                                <th data-sort="class">
                                    <i class="fas fa-fist-raised me-1"></i>Class
                                </th>
                                <th data-sort="spec">
                                    <i class="fas fa-cog me-1"></i>Spec
                                </th>
                                <th data-sort="cloak">
                                    <i class="fas fa-user-shield me-1"></i>Cloak
                                </th>
                                <th data-sort="tier-pieces">
                                    <i class="fas fa-layer-group me-1"></i>Tier
                                </th>
                                <th data-sort="tier">
                                    <i class="fas fa-gem me-1"></i>Tier Token
                                </th>
                                {% if is_authenticated %}
                                <th>
                                    <i class="fas fa-cogs me-1"></i>Actions
                                </th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for character in characters %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if character.url and character.url != '#' %}
                                            <a href="{{ character.url }}" target="_blank"
                                               class="text-decoration-none text-primary fw-bold">
                                                {{ character.name }}
                                                <i class="fas fa-external-link-alt ms-1 small"></i>
                                            </a>
                                        {% else %}
                                            <span class="fw-bold">{{ character.name }}</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="ilvl">{{ character.ilvl }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if character.role == "Tank" %}
                                            <i class="fas fa-shield-alt text-warning me-2"></i>
                                        {% elif character.role == "Healer" %}
                                            <i class="fas fa-plus text-success me-2"></i>
                                        {% elif character.role == "Melee DPS" or character.role == "Melee" %}
                                            <i class="fas fa-fist-raised text-danger me-2"></i>
                                        {% else %}
                                            <i class="fas fa-magic text-info me-2"></i>
                                        {% endif %}
                                        <span class="role-{{ character.role.lower().replace(' ', '-') }}">
                                            {{ character.role }}
                                        </span>
                                        {% if character.has_override %}
                                            <i class="fas fa-edit text-warning ms-2" title="Role Override Active" data-bs-toggle="tooltip"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="class-{{ character.class_name.lower().replace(' ', '-') }} fw-bold">
                                        {{ character.class_name }}
                                    </span>
                                </td>
                                <td>
                                    {{ character.spec }}
                                    {% if character.has_override %}
                                        <i class="fas fa-edit text-warning ms-2" title="Spec Override Active" data-bs-toggle="tooltip"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex flex-column gap-1">
                                        <div class="cloak-visual d-flex align-items-center gap-1">
                                            {% for i in range(6) %}
                                                <div class="cloak-block {% if i < character.back_rank_number %}cloak-filled{% else %}cloak-empty{% endif %}"></div>
                                            {% endfor %}
                                            <span class="cloak-count ms-2 small text-secondary">{{ character.back_rank_number }}/6</span>
                                        </div>
                                        <div class="pure-fiber-status">
                                            {% if character.has_pure_fiber %}
                                                <i class="fas fa-check-circle text-success" title="Pure Fiber"></i>
                                            {% else %}
                                                <i class="fas fa-times-circle text-danger" title="No Pure Fiber"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="tier-visual d-flex align-items-center gap-1">
                                        {% for i in range(5) %}
                                            <div class="tier-block {% if i < character.tier_pieces %}{% if character.tier_pieces >= 4 %}tier-filled-green{% else %}tier-filled-red{% endif %}{% else %}tier-empty{% endif %}"></div>
                                        {% endfor %}
                                        <span class="tier-count ms-2 small text-secondary">{{ character.tier_pieces }}/5</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column gap-1">
                                        <span class="badge bg-tertiary border-custom">
                                            {{ character.tier_token }}
                                        </span>
                                        {% if character.raid_token_1 or character.raid_token_2 %}
                                        <div class="raid-tokens-display d-flex gap-2">
                                            {% if character.raid_token_1 %}
                                            <small class="text-success">
                                                <i class="fas fa-calendar-check me-1"></i>{{ character.raid_token_1 }}
                                            </small>
                                            {% endif %}
                                            {% if character.raid_token_2 %}
                                            <small class="text-success">
                                                <i class="fas fa-calendar-check me-1"></i>{{ character.raid_token_2 }}
                                            </small>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </td>
                                {% if is_authenticated %}
                                <td>
                                    <button class="btn btn-sm btn-outline-primary edit-role-btn"
                                            data-character="{{ character.name }}"
                                            data-realm="{{ character.realm }}"
                                            data-current-role="{{ character.role }}"
                                            data-current-spec="{{ character.spec }}"
                                            data-original-role="{{ character.original_role or character.role }}"
                                            data-original-spec="{{ character.original_spec or character.spec }}"
                                            data-has-override="{{ character.has_override }}"
                                            data-raid-token-1="{% if character.raid_token_1 %}{{ character.raid_token_1 }}{% endif %}"
                                            data-raid-token-2="{% if character.raid_token_2 %}{{ character.raid_token_2 }}{% endif %}">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </button>
                                    {% if character.has_override %}
                                    <button class="btn btn-sm btn-outline-danger ms-1 remove-override-btn"
                                            data-character="{{ character.name }}"
                                            data-realm="{{ character.realm }}">
                                        <i class="fas fa-times me-1"></i>Remove
                                    </button>
                                    {% endif %}
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% if is_authenticated %}
<!-- Role Override Modal -->
<div class="modal fade" id="roleOverrideModal" tabindex="-1" aria-labelledby="roleOverrideModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="roleOverrideModalLabel">Edit Character Data</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="roleOverrideForm">
                    <input type="hidden" id="overrideCharacterName" name="character_name">
                    <input type="hidden" id="overrideRealm" name="realm">

                    <div class="mb-3">
                        <label class="form-label">Character</label>
                        <div id="characterInfo" class="form-control-plaintext"></div>
                    </div>

                    <div class="mb-3">
                        <label for="overrideRole" class="form-label">Role</label>
                        <select class="form-select" id="overrideRole" name="override_role" required>
                            <option value="Tank">Tank</option>
                            <option value="Healer">Healer</option>
                            <option value="Melee DPS">Melee DPS</option>
                            <option value="Ranged DPS">Ranged DPS</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="overrideSpec" class="form-label">Specialization</label>
                        <input type="text" class="form-control" id="overrideSpec" name="override_spec" required>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">
                        </i>Raid Tier Tokens
                        <small class="text-muted"></small>
                    </h6>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="raidToken1" class="form-label">First Token Date</label>
                                <input type="date" class="form-control" id="raidToken1" name="raid_token_1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="raidToken2" class="form-label">Second Token Date</label>
                                <input type="date" class="form-control" id="raidToken2" name="raid_token_2">
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info" id="originalInfo" style="display: none;">
                        <strong>Original:</strong> <span id="originalRoleSpec"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveOverrideBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
// Modern counter update function
function updateCounters() {
    const rows = document.querySelectorAll('.table-modern tbody tr:not([style*="display: none"])');
    const roleCounters = {
        'Tank': 0,
        'Healer': 0,
        'Melee': 0,
        'Ranged': 0
    };


    const availableClasses = new Set();
    let totalIlvl = 0;
    let characterCount = 0;

    rows.forEach(row => {
        // Count roles (now column 2 instead of 3)
        const roleCell = row.cells[2]; // Role column
        const roleText = roleCell.textContent.trim();

        if (roleText.includes('Tank')) roleCounters['Tank']++;
        else if (roleText.includes('Healer')) roleCounters['Healer']++;
        else if (roleText.includes('Melee')) roleCounters['Melee']++;
        else if (roleText.includes('Ranged')) roleCounters['Ranged']++;



        // Track available classes for buff/debuff counters (now column 3 instead of 4)
        const classCell = row.cells[3]; // Class column
        const className = classCell.textContent.trim();
        availableClasses.add(className);

        // Calculate average ilvl (now column 1 instead of 2)
        const ilvlCell = row.cells[1]; // iLvl column
        if (ilvlCell) {
            const ilvl = parseInt(ilvlCell.textContent.trim());
            if (!isNaN(ilvl)) {
                totalIlvl += ilvl;
                characterCount++;
            }
        }
    });

    // Update role counters in stats cards
    document.querySelector('.role-tank-count').textContent = roleCounters['Tank'];
    document.querySelector('.role-healer-count').textContent = roleCounters['Healer'];
    document.querySelector('.role-melee-count').textContent = roleCounters['Melee'];
    document.querySelector('.role-ranged-count').textContent = roleCounters['Ranged'];



    // Update average ilvl
    const avgIlvl = characterCount > 0 ? Math.round(totalIlvl / characterCount) : 0;
    const ilvlDisplay = document.getElementById('avgIlvl');
    if (ilvlDisplay) {
        ilvlDisplay.textContent = avgIlvl;
    }

    // Update buff/debuff counters
    const buffCounters = document.querySelectorAll('.buff-counter, .debuff-counter');
    buffCounters.forEach(counter => {
        const requiredClass = counter.dataset.class;
        if (availableClasses.has(requiredClass)) {
            counter.classList.remove('unavailable');
            counter.classList.add('available');
        } else {
            counter.classList.remove('available');
            counter.classList.add('unavailable');
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize buff counters as unavailable
    const buffCounters = document.querySelectorAll('.buff-counter, .debuff-counter');
    buffCounters.forEach(counter => {
        counter.classList.add('unavailable');
    });

    // Update counters initially
    updateCounters();

    // Sort table by role initially
    const table = document.querySelector('.table-modern');
    if (table) {
        const roleHeader = table.querySelector('[data-sort="role"]');
        if (roleHeader) {
            roleHeader.click();
        }
    }

    // Role override functionality
    const editRoleButtons = document.querySelectorAll('.edit-role-btn');
    const removeOverrideButtons = document.querySelectorAll('.remove-override-btn');
    const roleOverrideModal = document.getElementById('roleOverrideModal');
    const saveOverrideBtn = document.getElementById('saveOverrideBtn');

    if (editRoleButtons.length > 0) {
        editRoleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const characterName = this.dataset.character;
                const realm = this.dataset.realm;
                const currentRole = this.dataset.currentRole;
                const currentSpec = this.dataset.currentSpec;
                const originalRole = this.dataset.originalRole;
                const originalSpec = this.dataset.originalSpec;
                const hasOverride = this.dataset.hasOverride === 'True';
                const raidToken1 = this.getAttribute('data-raid-token-1');
                const raidToken2 = this.getAttribute('data-raid-token-2');

                // Populate modal
                document.getElementById('overrideCharacterName').value = characterName;
                document.getElementById('overrideRealm').value = realm;
                document.getElementById('characterInfo').textContent = `${characterName} - ${realm}`;
                document.getElementById('overrideRole').value = currentRole;
                document.getElementById('overrideSpec').value = currentSpec;

                // Set raid token dates, ensuring they're not undefined, null, or 'None'
                document.getElementById('raidToken1').value = (raidToken1 && raidToken1 !== 'None' && raidToken1 !== 'undefined') ? raidToken1 : '';
                document.getElementById('raidToken2').value = (raidToken2 && raidToken2 !== 'None' && raidToken2 !== 'undefined') ? raidToken2 : '';

                // Show original info if there's an override
                if (hasOverride) {
                    document.getElementById('originalRoleSpec').textContent = `${originalRole} - ${originalSpec}`;
                    document.getElementById('originalInfo').style.display = 'block';
                } else {
                    document.getElementById('originalInfo').style.display = 'none';
                }

                // Show modal
                const modal = new bootstrap.Modal(roleOverrideModal);
                modal.show();
            });
        });
    }

    if (removeOverrideButtons.length > 0) {
        removeOverrideButtons.forEach(button => {
            button.addEventListener('click', function() {
                const characterName = this.dataset.character;
                const realm = this.dataset.realm;

                if (confirm(`Remove role override for ${characterName}?`)) {
                    removeRoleOverride(characterName, realm);
                }
            });
        });
    }

    if (saveOverrideBtn) {
        saveOverrideBtn.addEventListener('click', function() {
            const form = document.getElementById('roleOverrideForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            saveRoleOverride(data);
        });
    }
});

// Role override API functions
async function saveRoleOverride(data) {
    try {
        const response = await fetch('/api/character_data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok) {
            alert('Character data saved successfully!');
            location.reload();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to save character data');
    }
}

async function removeRoleOverride(characterName, realm) {
    try {
        const response = await fetch(`/api/role_overrides/${encodeURIComponent(characterName)}/${encodeURIComponent(realm)}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
            alert('Role override removed successfully!');
            location.reload();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to remove role override');
    }
}
</script>

<!-- Additional CSS for buff/debuff styling -->
<style>
/* Mini Role Counters */
.role-mini {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.role-mini:hover {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.role-mini i {
    font-size: 0.75rem;
    opacity: 0.8;
}

.role-mini.tank { color: var(--role-tank, #f39c12); }
.role-mini.healer { color: var(--role-healer, #27ae60); }
.role-mini.melee { color: var(--role-melee, #e74c3c); }
.role-mini.ranged { color: var(--role-ranged, #3498db); }

/* Mini Buff/Debuff Icons */
.buff-icon-mini, .debuff-icon-mini {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    object-fit: cover;
}

.buff-counter, .debuff-counter {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
}


.buff-counter.available, .debuff-counter.available {
    border-color: #28a745;
    box-shadow: 0 0 6px rgba(40, 167, 69, 0.4);
    background: rgba(40, 167, 69, 0.1);
}

.buff-counter.unavailable, .debuff-counter.unavailable {
    border-color: #444 !important;
    background: rgba(0, 0, 0, 0.8) !important;
    opacity: 0.3 !important;
    box-shadow: none !important;
}

.buff-icon, .debuff-icon {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    object-fit: cover;
}

.buff-counter.unavailable .buff-icon,
.debuff-counter.unavailable .debuff-icon,
.buff-counter.unavailable .buff-icon-mini,
.debuff-counter.unavailable .debuff-icon-mini {
    filter: grayscale(100%) brightness(0.3) !important;
}



/* Tier visualization blocks */
.tier-visual {
    min-width: 120px;
}

.tier-block {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

.tier-filled-red {
    background-color: #dc3545;
    border-color: #dc3545;
    box-shadow: 0 0 4px rgba(220, 53, 69, 0.4);
}

.tier-filled-green {
    background-color: #28a745;
    border-color: #28a745;
    box-shadow: 0 0 4px rgba(40, 167, 69, 0.4);
}

.tier-empty {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.tier-count {
    font-weight: 500;
    min-width: 30px;
}

/* Cloak visualization blocks */
.cloak-visual {
    min-width: 140px;
}

.cloak-block {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

.cloak-filled {
    background-color: #17a2b8;
    border-color: #17a2b8;
    box-shadow: 0 0 4px rgba(23, 162, 184, 0.4);
}

.cloak-empty {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.cloak-count {
    font-weight: 500;
    min-width: 30px;
}

.pure-fiber-status {
    display: flex;
    justify-content: center;
    margin-top: 2px;
}

/* Role Override Indicators */
.edit-role-btn {
    min-width: 70px;
}

.remove-override-btn {
    min-width: 80px;
}

.fa-edit.text-warning {
    opacity: 0.8;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.8; }
    50% { opacity: 1; }
    100% { opacity: 0.8; }
}

/* Modal styling */
.modal-content {
    background: black;
    border: 1px solid var(--border-color);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
}

/* Raid Token Display */
.raid-tokens-display {
    font-size: 0.75rem;
    line-height: 1.2;
}

.raid-tokens-display small {
    opacity: 0.9;
}

.raid-tokens-display .fas {
    font-size: 0.7rem;
}
</style>
{% endblock %}
