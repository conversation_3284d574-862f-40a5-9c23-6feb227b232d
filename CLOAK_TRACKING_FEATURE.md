# Legendary Cloak Tracking Feature

## Overview
This feature adds tracking for the new legendary cloak "<PERSON><PERSON><PERSON> Wraps" which grows in power each week. The system now tracks both the cloak's rank and the specific socket type (fiber) it contains.

## New Fields Added

### PowerShell Scripts (Main.ps1 & Main copy.ps1)
- **Back_rank**: Tracks the rank of the cloak (e.g., "Rank 3")
  - Source: `$Back.name_description.display_string`
  - Logic: `if($null -eq $Back.name_description){'-'}else{$Back.name_description.display_string}`

- **Back_socket_type**: Tracks the specific socket type/fiber (e.g., "Energizing Fiber")
  - Source: `$Back.sockets[0].display_string`
  - Logic: `if($Back.sockets.count -eq 0){'-'}elseif($Back.sockets.count -eq 1){$Back.sockets[0].display_string}`

### Web Application (app.py)
Updated the character gear object to include:
```python
'back': {
    'name': row['Back'], 
    'rank': row.get('Back_rank', '-'), 
    'socket': row['Back_sock'], 
    'gem': row['Back_gem'], 
    'socket_type': row.get('Back_socket_type', '-'), 
    'enchant': row['Back_enchant']
}
```

### Web Interface (templates/inspect.html)
Enhanced the Back item display to show:
- Cloak rank as a blue badge next to the item name
- Socket type information below the gem information (in orange text)

### Index Page (templates/index.html)
Replaced the "Armor" column with a "Cloak" column that displays:
- Cloak rank as a blue badge (e.g., "Rank 3") or "No Cloak" for characters without legendary cloaks
- Pure Fiber indicator in green text with gem icon for optimal socket gems
- Other fiber types in orange text with circle icon

## Data Structure Example

### Input Data (from Blizzard API)
```powershell
item = @{
    name = "Reshii Wraps"
    level = @{ value = 707; display_string = "Item Level 707" }
    name_description = @{ display_string = "Rank 3"; color = "" }
    sockets = @(
        @{
            socket_type = ""
            item = ""
            context = 11
            display_string = "Energizing Fiber"
            media = ""
        }
    )
    quality = @{ type = "ARTIFACT"; name = "Artifact" }
    # ... other properties
}
```

### Output Data (CSV Export)
```csv
Back,Back_rank,Back_sock,Back_gem,Back_socket_type,Back_enchant
"Reshii Wraps (707)","Rank 3","1","Chronomantic Fiber","Energizing Fiber","0"
```

### Web Display

**Character Detail Page:**
```
Back: Reshii Wraps (707) [Rank 3]
      Chronomantic Fiber
      Socket: Energizing Fiber
```

**Index Page Cloak Column:**
```
[Rank 3]
💎 Pure Fiber
```
or
```
[No Cloak]
```
or
```
[Rank 2]
● Energizing Fiber
```

## Files Modified

1. **PS_version/Main.ps1** - Added Back_rank and Back_socket_type fields
2. **PS_version/Main copy.ps1** - Added Back_rank and Back_socket_type fields
3. **app.py** - Updated character gear object and index page to include cloak data
4. **templates/inspect.html** - Enhanced UI to display rank and socket type
5. **templates/index.html** - Replaced Armor column with Cloak column showing rank and Pure Fiber status

## Testing

### PowerShell Test
- Created `PS_version/test_cloak_simple.ps1` to verify field extraction
- Tests confirm both rank and socket type are correctly parsed

### Python Test  
- Created `test_cloak_webapp.py` to verify web app integration
- Tests confirm CSV processing and character object creation work correctly

### Test Results
```
✓ Back_rank field correctly processed: "Rank 3"
✓ Back_socket_type field correctly processed: "Energizing Fiber"
✓ Character object creation successful
✓ All tests passed!
```

## Usage

1. **Data Collection**: Run the PowerShell script to collect character data
   ```powershell
   .\PS_version\Main.ps1
   ```

2. **Web Interface**: View character details to see cloak rank and socket information
   - Navigate to character inspect page
   - Look for the Back item section
   - Rank appears as a blue badge
   - Socket type appears below gem information

## Future Enhancements

- Track rank progression over time
- Alert when cloak rank increases
- Compare socket types across characters
- Add filtering/sorting by cloak rank in character lists

## Notes

- The feature gracefully handles characters without legendary cloaks (shows '-' for missing data)
- Compatible with existing data structure and doesn't break backward compatibility
- Socket type tracking works for any socketed back item, not just legendary cloaks
